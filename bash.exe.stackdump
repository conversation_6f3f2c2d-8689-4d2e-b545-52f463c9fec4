Stack trace:
Frame         Function      Args
0007FFFFB740  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFFA640) msys-2.0.dll+0x1FE8E
0007FFFFB740  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA18) msys-2.0.dll+0x67F9
0007FFFFB740  000210046832 (000210286019, 0007FFFFB5F8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFB740  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFB740  000210068E24 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBA20  00021006A225 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF89B6F0000 ntdll.dll
7FF899670000 KERNEL32.DLL
7FF898C10000 KERNELBASE.dll
7FF89AC40000 USER32.dll
7FF8992D0000 win32u.dll
000210040000 msys-2.0.dll
7FF899810000 GDI32.dll
7FF898AF0000 gdi32full.dll
7FF8991C0000 msvcp_win.dll
7FF899300000 ucrtbase.dll
7FF89A870000 advapi32.dll
7FF899760000 msvcrt.dll
7FF89B120000 sechost.dll
7FF898A00000 bcrypt.dll
7FF89AE40000 RPCRT4.dll
7FF898190000 CRYPTBASE.DLL
7FF899140000 bcryptPrimitives.dll
7FF8999E0000 IMM32.DLL
