2025-08-24 14:53:25 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-24 14:53:25 [main] INFO  org.ruoyi.RuoYiAIApplication - Starting RuoYiAIApplication using Java 17.0.12 with PID 18388 (D:\daima\new\ruoyi-ai\ruoyi-admin\target\classes started by 86153 in D:\daima\new\ruoyi-ai)
2025-08-24 14:53:25 [main] INFO  org.ruoyi.RuoYiAIApplication - The following 1 profile is active: "dev"
2025-08-24 14:53:35 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-24 14:53:35 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-08-24 14:53:35 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-08-24 14:53:36 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@a94c8e3
2025-08-24 14:53:36 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-08-24 14:53:36 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-08-24 14:53:36 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-24 14:53:39 [main] INFO  o.r.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-08-24 14:53:39 [main] INFO  o.r.common.redis.config.RedisConfig - 初始化 redis 配置
2025-08-24 14:53:39 [main] INFO  org.redisson.Version - Redisson 3.20.1
2025-08-24 14:53:39 [redisson-netty-2-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-08-24 14:53:39 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-08-24 14:53:41 [main] INFO  o.r.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-08-24 14:53:42 [HttpClient-1-Worker-1] INFO  i.m.client.McpAsyncClient - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[experimental=null, logging=LoggingCapabilities[], prompts=null, resources=null, tools=ToolCapabilities[listChanged=true]], Info: Implementation[name=ruoyi-mcp-serve, version=1.0.0] and Instructions null
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error code EPERM
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error syscall open
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error path D:\nodejs\node_cache\_cacache\tmp\8e50c2e2
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error errno EPERM
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error FetchError: Invalid response body while trying to fetch https://registry.npmmirror.com/@modelcontextprotocol%2fserver-filesystem: EPERM: operation not permitted, open 'D:\nodejs\node_cache\_cacache\tmp\8e50c2e2'
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error     at D:\nodejs\node_modules\npm\node_modules\minipass-fetch\lib\body.js:170:15
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error     at async Response.json (D:\nodejs\node_modules\npm\node_modules\minipass-fetch\lib\body.js:75:17)
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error     at async RegistryFetcher.packument (D:\nodejs\node_modules\npm\node_modules\pacote\lib\registry.js:98:25)
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error     at async RegistryFetcher.manifest (D:\nodejs\node_modules\npm\node_modules\pacote\lib\registry.js:128:23)
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error     at async getManifest (D:\nodejs\node_modules\npm\node_modules\libnpmexec\lib\index.js:27:22)
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error     at async missingFromTree (D:\nodejs\node_modules\npm\node_modules\libnpmexec\lib\index.js:60:22)
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error     at async D:\nodejs\node_modules\npm\node_modules\libnpmexec\lib\index.js:182:32
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error     at async Promise.all (index 0)
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error     at async exec (D:\nodejs\node_modules\npm\node_modules\libnpmexec\lib\index.js:180:3)
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error     at async Npm.exec (D:\nodejs\node_modules\npm\lib\npm.js:207:9) {
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error   code: 'EPERM',
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error   errno: 'EPERM',
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error   syscall: 'open',
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error   path: 'D:\\nodejs\\node_cache\\_cacache\\tmp\\8e50c2e2',
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error   type: 'system'
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error }
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error The operation was rejected by your operating system.
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error It's possible that the file was already in use (by a text editor or antivirus),
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error or that you lack permissions to access it.
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error If you believe this might be a permissions issue, please double-check the
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error permissions of the file and its containing directories, or try running
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error the command again as root/Administrator.
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm notice
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm notice New major version of npm available! 10.9.2 -> 11.5.2
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm notice Changelog: https://github.com/npm/cli/releases/tag/v11.5.2
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm notice To update run: npm install -g npm@11.5.2
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm notice
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error Log files were not written due to an error writing to the directory: D:\nodejs\node_cache\_logs
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error You can rerun the command with `--loglevel=verbose` to see the logs in your terminal
2025-08-24 14:58:42 [main] INFO  o.r.c.core.manager.ShutdownManager - ====关闭后台任务任务线程池====
2025-08-24 14:58:42 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-08-24 14:58:42 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-08-24 14:58:42 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-08-24 14:58:42 [main] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-08-24 14:58:42 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-08-24 15:22:18 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-24 15:22:18 [main] INFO  org.ruoyi.RuoYiAIApplication - Starting RuoYiAIApplication using Java 17.0.12 with PID 19268 (D:\daima\new\ruoyi-ai\ruoyi-admin\target\classes started by 86153 in D:\daima\new\ruoyi-ai)
2025-08-24 15:22:18 [main] INFO  org.ruoyi.RuoYiAIApplication - The following 1 profile is active: "dev"
2025-08-24 15:22:32 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-24 15:22:33 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-08-24 15:22:33 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-08-24 15:22:34 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@a94c8e3
2025-08-24 15:22:34 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-08-24 15:22:34 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-08-24 15:22:34 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-24 15:22:37 [main] INFO  o.r.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-08-24 15:22:37 [main] INFO  o.r.common.redis.config.RedisConfig - 初始化 redis 配置
2025-08-24 15:22:37 [main] INFO  org.redisson.Version - Redisson 3.20.1
2025-08-24 15:22:37 [redisson-netty-2-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-08-24 15:22:38 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-08-24 15:22:39 [main] INFO  o.r.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-08-24 15:22:40 [HttpClient-1-Worker-1] INFO  i.m.client.McpAsyncClient - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[experimental=null, logging=LoggingCapabilities[], prompts=null, resources=null, tools=ToolCapabilities[listChanged=true]], Info: Implementation[name=ruoyi-mcp-serve, version=1.0.0] and Instructions null
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error code EPERM
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error syscall open
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error path D:\nodejs\node_cache\_cacache\tmp\8559c774
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error errno EPERM
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error FetchError: Invalid response body while trying to fetch https://registry.npmmirror.com/@modelcontextprotocol%2fserver-filesystem: EPERM: operation not permitted, open 'D:\nodejs\node_cache\_cacache\tmp\8559c774'
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error     at D:\nodejs\node_modules\npm\node_modules\minipass-fetch\lib\body.js:170:15
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error     at async Response.json (D:\nodejs\node_modules\npm\node_modules\minipass-fetch\lib\body.js:75:17)
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error     at async RegistryFetcher.packument (D:\nodejs\node_modules\npm\node_modules\pacote\lib\registry.js:98:25)
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error     at async RegistryFetcher.manifest (D:\nodejs\node_modules\npm\node_modules\pacote\lib\registry.js:128:23)
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error     at async getManifest (D:\nodejs\node_modules\npm\node_modules\libnpmexec\lib\index.js:27:22)
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error     at async missingFromTree (D:\nodejs\node_modules\npm\node_modules\libnpmexec\lib\index.js:60:22)
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error     at async D:\nodejs\node_modules\npm\node_modules\libnpmexec\lib\index.js:182:32
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error     at async Promise.all (index 0)
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error     at async exec (D:\nodejs\node_modules\npm\node_modules\libnpmexec\lib\index.js:180:3)
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error     at async Npm.exec (D:\nodejs\node_modules\npm\lib\npm.js:207:9) {
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error   code: 'EPERM',
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error   errno: 'EPERM',
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error   syscall: 'open',
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error   path: 'D:\\nodejs\\node_cache\\_cacache\\tmp\\8559c774',
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error   type: 'system'
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error }
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error The operation was rejected by your operating system.
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error It's possible that the file was already in use (by a text editor or antivirus),
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error or that you lack permissions to access it.
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error If you believe this might be a permissions issue, please double-check the
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error permissions of the file and its containing directories, or try running
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error the command again as root/Administrator.
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm notice
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm notice New major version of npm available! 10.9.2 -> 11.5.2
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm notice Changelog: https://github.com/npm/cli/releases/tag/v11.5.2
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm notice To update run: npm install -g npm@11.5.2
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm notice
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error Log files were not written due to an error writing to the directory: D:\nodejs\node_cache\_logs
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error You can rerun the command with `--loglevel=verbose` to see the logs in your terminal
2025-08-24 15:25:13 [main] INFO  o.r.c.core.manager.ShutdownManager - ====关闭后台任务任务线程池====
2025-08-24 15:25:13 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-08-24 15:25:13 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-08-24 15:25:13 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-08-24 15:25:13 [main] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-08-24 15:25:13 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-08-24 15:29:58 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-24 15:29:58 [main] INFO  org.ruoyi.RuoYiAIApplication - Starting RuoYiAIApplication using Java 17.0.12 with PID 2284 (D:\daima\new\ruoyi-ai\ruoyi-admin\target\classes started by 86153 in D:\daima\new\ruoyi-ai)
2025-08-24 15:29:58 [main] INFO  org.ruoyi.RuoYiAIApplication - The following 1 profile is active: "dev"
2025-08-24 15:30:04 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-24 15:30:04 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-08-24 15:30:04 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-08-24 15:30:05 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@8a0a1d1
2025-08-24 15:30:05 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-08-24 15:30:05 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-08-24 15:30:05 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-24 15:30:07 [main] INFO  o.r.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-08-24 15:30:07 [main] INFO  o.r.common.redis.config.RedisConfig - 初始化 redis 配置
2025-08-24 15:30:07 [main] INFO  org.redisson.Version - Redisson 3.20.1
2025-08-24 15:30:08 [redisson-netty-2-4] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-08-24 15:30:08 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-08-24 15:30:09 [main] INFO  o.r.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-08-24 15:30:10 [HttpClient-1-Worker-1] INFO  i.m.client.McpAsyncClient - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[experimental=null, logging=LoggingCapabilities[], prompts=null, resources=null, tools=ToolCapabilities[listChanged=true]], Info: Implementation[name=ruoyi-mcp-serve, version=1.0.0] and Instructions null
2025-08-24 15:30:13 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: Secure MCP Filesystem Server running on stdio
2025-08-24 15:30:13 [pool-3-thread-1] INFO  i.m.client.McpAsyncClient - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[experimental=null, logging=null, prompts=null, resources=null, tools=ToolCapabilities[listChanged=null]], Info: Implementation[name=secure-filesystem-server, version=0.2.0] and Instructions null
2025-08-24 15:30:13 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: Client does not support MCP Roots, using allowed directories set from server args: [ 'D:\\ruoyi-mcp' ]
2025-08-24 15:30:15 [pool-9-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: [2025-08-24T07:30:15.672Z] Starting Search1API MCP server
2025-08-24 15:30:15 [pool-9-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: [2025-08-24T07:30:15.673Z] Creating Search1API MCP server
2025-08-24 15:30:15 [pool-9-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: [2025-08-24T07:30:15.674Z] Server started successfully
2025-08-24 15:30:15 [pool-9-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: [2025-08-24T07:30:15.674Z] Server started successfully
2025-08-24 15:30:15 [pool-7-thread-1] INFO  i.m.client.McpAsyncClient - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[experimental=null, logging=null, prompts=null, resources=ResourceCapabilities[subscribe=null, listChanged=null], tools=ToolCapabilities[listChanged=null]], Info: Implementation[name=search1api-server, version=1.0.0] and Instructions null
2025-08-24 15:30:20 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-08-24 15:30:20 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-24 15:30:20 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-24 15:30:20 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-24 15:30:20 [main] INFO  org.ruoyi.RuoYiAIApplication - Started RuoYiAIApplication in 22.759 seconds (process running for 23.92)
2025-08-24 15:30:20 [main] INFO  o.r.c.c.l.WebSocketTopicListener - 初始化WebSocket主题订阅监听器成功
2025-08-24 15:30:20 [main] INFO  o.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-08-24 15:30:20 [RMI TCP Connection(4)-172.30.16.1] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-24 15:34:04 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 15:34:08 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 15:34:08 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 15:34:08 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 15:34:10 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 15:34:10 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 15:34:10 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 15:34:24 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 15:34:24 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 15:34:24 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 15:34:26 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 15:35:26 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 15:35:26 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 15:35:53 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 15:36:10 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:--> POST https://api.ppinfra.com/openai/v1/chat/completions
2025-08-24 15:36:10 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Content-Type: application/json; charset=utf-8
2025-08-24 15:36:10 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Content-Length: 855
2025-08-24 15:36:10 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Accept: text/event-stream
2025-08-24 15:36:10 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:--> END POST
2025-08-24 15:36:11 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:<-- 200 https://api.ppinfra.com/openai/v1/chat/completions (1057ms)
2025-08-24 15:36:11 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:date: Sun, 24 Aug 2025 07:34:56 GMT
2025-08-24 15:36:11 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:content-type: text/event-stream
2025-08-24 15:36:11 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:cache-control: no-cache
2025-08-24 15:36:11 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:x-request-id: 
2025-08-24 15:36:11 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:x-trace-id: f0db9011a8edecae95290a62c3b48fba
2025-08-24 15:36:11 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:<-- END HTTP
2025-08-24 15:36:11 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.l.SSEEventSourceListener - OpenAI建立sse连接...
2025-08-24 15:36:12 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 15:36:12 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.l.SSEEventSourceListener - OpenAI关闭sse连接...
2025-08-24 15:36:57 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 15:37:46 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:--> POST https://api.ppinfra.com/openai/v1/chat/completions
2025-08-24 15:37:46 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Content-Type: application/json; charset=utf-8
2025-08-24 15:37:46 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Content-Length: 22808
2025-08-24 15:37:46 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Accept: text/event-stream
2025-08-24 15:37:46 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:--> END POST
2025-08-24 15:37:49 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:<-- 200 https://api.ppinfra.com/openai/v1/chat/completions (2263ms)
2025-08-24 15:37:49 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:date: Sun, 24 Aug 2025 07:36:34 GMT
2025-08-24 15:37:49 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:content-type: text/event-stream
2025-08-24 15:37:49 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:cache-control: no-cache
2025-08-24 15:37:49 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:x-request-id: 
2025-08-24 15:37:49 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:x-trace-id: 471296bf77970c58f270b6f70b8e65fb
2025-08-24 15:37:49 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:<-- END HTTP
2025-08-24 15:37:49 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.l.SSEEventSourceListener - OpenAI建立sse连接...
2025-08-24 15:38:38 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 15:38:38 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.l.SSEEventSourceListener - OpenAI关闭sse连接...
2025-08-24 15:46:31 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 15:50:02 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 15:50:02 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 15:50:37 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 15:50:37 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:11:28 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:11:28 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:11:33 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:11:33 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:11:36 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:11:36 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:12:23 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:12:23 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:14:31 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:14:31 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:14:36 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:14:38 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:14:43 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:14:53 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:14:55 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:15:01 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:22:16 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:22:18 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:22:18 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:22:19 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:22:19 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:22:21 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:22:21 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:22:24 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:22:25 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:22:26 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:22:26 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:22:27 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:22:28 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:22:28 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:22:29 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:22:30 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:22:30 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:22:33 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:22:33 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:22:35 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:22:35 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:22:36 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:22:36 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:22:37 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:36:02 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:36:02 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:36:02 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:36:02 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:36:02 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:36:03 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:36:03 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:36:14 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:36:17 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:36:17 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:36:27 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:36:28 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:36:50 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:36:50 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:36:55 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:36:55 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:--> POST http://192.168.241.130:3000/api/v1/chat/completions
2025-08-24 16:36:55 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Content-Type: application/json; charset=utf-8
2025-08-24 16:36:55 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Content-Length: 540
2025-08-24 16:36:55 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Accept: text/event-stream
2025-08-24 16:36:55 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:--> END POST
2025-08-24 16:36:55 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:<-- 200 OK http://192.168.241.130:3000/api/v1/chat/completions (241ms)
2025-08-24 16:36:55 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:X-Frame-Options: DENY
2025-08-24 16:36:55 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:X-Content-Type-Options: nosniff
2025-08-24 16:36:55 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:X-XSS-Protection: 1; mode=block
2025-08-24 16:36:55 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Referrer-Policy: strict-origin-when-cross-origin
2025-08-24 16:36:55 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Permissions-Policy: geolocation=(self), microphone=(self), camera=(self)
2025-08-24 16:36:55 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Connection: keep-alive
2025-08-24 16:36:55 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Content-Type: text/event-stream;charset=utf-8
2025-08-24 16:36:55 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Access-Control-Allow-Origin: *
2025-08-24 16:36:55 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:X-Accel-Buffering: no
2025-08-24 16:36:55 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Cache-Control: no-cache, no-transform
2025-08-24 16:36:55 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Date: Fri, 22 Aug 2025 12:56:15 GMT
2025-08-24 16:36:55 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Transfer-Encoding: chunked
2025-08-24 16:36:55 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:<-- END HTTP
2025-08-24 16:36:55 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.l.FastGPTSSEEventSourceListener - FastGPT  sse连接成功
2025-08-24 16:37:24 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.l.FastGPTSSEEventSourceListener - FastGPT  sse连接关闭
2025-08-24 16:37:24 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:43:16 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:43:16 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:--> POST http://192.168.241.130:3000/api/v1/chat/completions
2025-08-24 16:43:16 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Content-Type: application/json; charset=utf-8
2025-08-24 16:43:16 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Content-Length: 6518
2025-08-24 16:43:16 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Accept: text/event-stream
2025-08-24 16:43:16 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:--> END POST
2025-08-24 16:43:16 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:<-- 200 OK http://192.168.241.130:3000/api/v1/chat/completions (29ms)
2025-08-24 16:43:16 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:X-Frame-Options: DENY
2025-08-24 16:43:16 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:X-Content-Type-Options: nosniff
2025-08-24 16:43:16 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:X-XSS-Protection: 1; mode=block
2025-08-24 16:43:16 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Referrer-Policy: strict-origin-when-cross-origin
2025-08-24 16:43:16 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Permissions-Policy: geolocation=(self), microphone=(self), camera=(self)
2025-08-24 16:43:16 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Connection: keep-alive
2025-08-24 16:43:16 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Content-Type: text/event-stream;charset=utf-8
2025-08-24 16:43:16 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Access-Control-Allow-Origin: *
2025-08-24 16:43:16 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:X-Accel-Buffering: no
2025-08-24 16:43:16 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Cache-Control: no-cache, no-transform
2025-08-24 16:43:16 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Date: Fri, 22 Aug 2025 13:02:37 GMT
2025-08-24 16:43:16 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Transfer-Encoding: chunked
2025-08-24 16:43:16 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:<-- END HTTP
2025-08-24 16:43:16 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.l.FastGPTSSEEventSourceListener - FastGPT  sse连接成功
2025-08-24 16:43:46 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.l.FastGPTSSEEventSourceListener - FastGPT  sse连接关闭
2025-08-24 16:43:46 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:44:32 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-08-24 16:44:32 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-24 16:44:34 [SpringApplicationShutdownHook] INFO  o.r.c.core.manager.ShutdownManager - ====关闭后台任务任务线程池====
2025-08-24 16:44:34 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-08-24 16:44:34 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-08-24 16:44:34 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-08-24 16:44:34 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-08-24 16:44:34 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-08-24 16:44:44 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-24 16:44:44 [main] INFO  org.ruoyi.RuoYiAIApplication - Starting RuoYiAIApplication using Java 17.0.12 with PID 5016 (D:\daima\new\ruoyi-ai\ruoyi-admin\target\classes started by 86153 in D:\daima\new\ruoyi-ai)
2025-08-24 16:44:44 [main] INFO  org.ruoyi.RuoYiAIApplication - The following 1 profile is active: "dev"
2025-08-24 16:44:51 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-24 16:44:51 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-08-24 16:44:51 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-08-24 16:44:52 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@16bbb59c
2025-08-24 16:44:52 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-08-24 16:44:52 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-08-24 16:44:52 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-24 16:44:54 [main] INFO  o.r.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-08-24 16:44:55 [main] INFO  o.r.common.redis.config.RedisConfig - 初始化 redis 配置
2025-08-24 16:44:55 [main] INFO  org.redisson.Version - Redisson 3.20.1
2025-08-24 16:44:55 [redisson-netty-2-4] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-08-24 16:44:55 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-08-24 16:44:56 [main] INFO  o.r.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-08-24 16:44:57 [HttpClient-1-Worker-1] INFO  i.m.client.McpAsyncClient - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[experimental=null, logging=LoggingCapabilities[], prompts=null, resources=null, tools=ToolCapabilities[listChanged=true]], Info: Implementation[name=ruoyi-mcp-serve, version=1.0.0] and Instructions null
2025-08-24 16:45:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: Secure MCP Filesystem Server running on stdio
2025-08-24 16:45:00 [pool-3-thread-1] INFO  i.m.client.McpAsyncClient - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[experimental=null, logging=null, prompts=null, resources=null, tools=ToolCapabilities[listChanged=null]], Info: Implementation[name=secure-filesystem-server, version=0.2.0] and Instructions null
2025-08-24 16:45:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: Client does not support MCP Roots, using allowed directories set from server args: [ 'D:\\ruoyi-mcp' ]
2025-08-24 16:45:02 [pool-9-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: [2025-08-24T08:45:02.000Z] Starting Search1API MCP server
2025-08-24 16:45:02 [pool-9-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: [2025-08-24T08:45:02.002Z] Creating Search1API MCP server
2025-08-24 16:45:02 [pool-9-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: [2025-08-24T08:45:02.003Z] Server started successfully
2025-08-24 16:45:02 [pool-9-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: [2025-08-24T08:45:02.003Z] Server started successfully
2025-08-24 16:45:02 [pool-7-thread-1] INFO  i.m.client.McpAsyncClient - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[experimental=null, logging=null, prompts=null, resources=ResourceCapabilities[subscribe=null, listChanged=null], tools=ToolCapabilities[listChanged=null]], Info: Implementation[name=search1api-server, version=1.0.0] and Instructions null
2025-08-24 16:45:07 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-08-24 16:45:07 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-24 16:45:07 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-24 16:45:07 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-24 16:45:07 [main] INFO  org.ruoyi.RuoYiAIApplication - Started RuoYiAIApplication in 23.724 seconds (process running for 24.995)
2025-08-24 16:45:07 [main] INFO  o.r.c.c.l.WebSocketTopicListener - 初始化WebSocket主题订阅监听器成功
2025-08-24 16:45:07 [main] INFO  o.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-08-24 16:45:07 [RMI TCP Connection(2)-172.30.16.1] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-24 16:45:35 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:45:37 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:--> POST http://192.168.241.130:3000/api/v1/chat/completions
2025-08-24 16:45:37 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Content-Type: application/json; charset=utf-8
2025-08-24 16:45:37 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Content-Length: 586
2025-08-24 16:45:37 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Accept: text/event-stream
2025-08-24 16:45:37 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:--> END POST
2025-08-24 16:45:47 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:<-- 200 OK http://192.168.241.130:3000/api/v1/chat/completions (10044ms)
2025-08-24 16:45:47 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:X-Frame-Options: DENY
2025-08-24 16:45:47 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:X-Content-Type-Options: nosniff
2025-08-24 16:45:47 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:X-XSS-Protection: 1; mode=block
2025-08-24 16:45:47 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Referrer-Policy: strict-origin-when-cross-origin
2025-08-24 16:45:47 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Permissions-Policy: geolocation=(self), microphone=(self), camera=(self)
2025-08-24 16:45:47 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Connection: keep-alive
2025-08-24 16:45:47 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Content-Type: text/event-stream;charset=utf-8
2025-08-24 16:45:47 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Access-Control-Allow-Origin: *
2025-08-24 16:45:47 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:X-Accel-Buffering: no
2025-08-24 16:45:47 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Cache-Control: no-cache, no-transform
2025-08-24 16:45:47 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Date: Fri, 22 Aug 2025 13:05:07 GMT
2025-08-24 16:45:47 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Transfer-Encoding: chunked
2025-08-24 16:45:47 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:<-- END HTTP
2025-08-24 16:45:47 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.l.FastGPTSSEEventSourceListener - FastGPT  sse连接成功
2025-08-24 16:46:43 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.l.FastGPTSSEEventSourceListener - FastGPT  sse连接关闭
2025-08-24 16:49:50 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:49:50 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:52:52 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:52:52 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:53:10 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:53:10 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
