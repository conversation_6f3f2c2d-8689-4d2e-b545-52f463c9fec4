2025-08-24 14:53:25 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-24 14:53:25 [main] INFO  org.ruoyi.RuoYiAIApplication - Starting RuoYiAIApplication using Java 17.0.12 with PID 18388 (D:\daima\new\ruoyi-ai\ruoyi-admin\target\classes started by 86153 in D:\daima\new\ruoyi-ai)
2025-08-24 14:53:25 [main] INFO  org.ruoyi.RuoYiAIApplication - The following 1 profile is active: "dev"
2025-08-24 14:53:34 [main] WARN  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lock4j-com.baomidou.lock.spring.boot.autoconfigure.Lock4jProperties' of type [com.baomidou.lock.spring.boot.autoconfigure.Lock4jProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [meterRegistryPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-24 14:53:34 [main] WARN  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.lock.spring.boot.autoconfigure.LockAutoConfiguration' of type [com.baomidou.lock.spring.boot.autoconfigure.LockAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [meterRegistryPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-24 14:53:34 [main] WARN  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lockKeyBuilder' of type [com.baomidou.lock.DefaultLockKeyBuilder] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [meterRegistryPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-24 14:53:34 [main] WARN  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lockFailureStrategy' of type [com.baomidou.lock.DefaultLockFailureStrategy] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [meterRegistryPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-24 14:53:34 [main] WARN  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lockInterceptor' of type [com.baomidou.lock.aop.LockInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [meterRegistryPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-24 14:53:34 [main] WARN  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lockAnnotationAdvisor' of type [com.baomidou.lock.aop.LockAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [meterRegistryPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-24 14:53:35 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-24 14:53:35 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-08-24 14:53:35 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-08-24 14:53:36 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@a94c8e3
2025-08-24 14:53:36 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-08-24 14:53:36 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-08-24 14:53:36 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-24 14:53:37 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "org.ruoyi.system.domain.CommentGoodbad".
2025-08-24 14:53:37 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class org.ruoyi.system.domain.CommentGoodbad ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-24 14:53:38 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "org.ruoyi.system.domain.Comment".
2025-08-24 14:53:38 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class org.ruoyi.system.domain.Comment ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-24 14:53:38 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "org.ruoyi.system.domain.NewsComment".
2025-08-24 14:53:38 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class org.ruoyi.system.domain.NewsComment ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-24 14:53:39 [main] INFO  o.r.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-08-24 14:53:39 [main] INFO  o.r.common.redis.config.RedisConfig - 初始化 redis 配置
2025-08-24 14:53:39 [main] INFO  org.redisson.Version - Redisson 3.20.1
2025-08-24 14:53:39 [redisson-netty-2-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-08-24 14:53:39 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-08-24 14:53:41 [main] INFO  o.r.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-08-24 14:53:42 [HttpClient-1-Worker-1] INFO  i.m.client.McpAsyncClient - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[experimental=null, logging=LoggingCapabilities[], prompts=null, resources=null, tools=ToolCapabilities[listChanged=true]], Info: Implementation[name=ruoyi-mcp-serve, version=1.0.0] and Instructions null
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error code EPERM
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error syscall open
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error path D:\nodejs\node_cache\_cacache\tmp\8e50c2e2
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error errno EPERM
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error FetchError: Invalid response body while trying to fetch https://registry.npmmirror.com/@modelcontextprotocol%2fserver-filesystem: EPERM: operation not permitted, open 'D:\nodejs\node_cache\_cacache\tmp\8e50c2e2'
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error     at D:\nodejs\node_modules\npm\node_modules\minipass-fetch\lib\body.js:170:15
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error     at async Response.json (D:\nodejs\node_modules\npm\node_modules\minipass-fetch\lib\body.js:75:17)
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error     at async RegistryFetcher.packument (D:\nodejs\node_modules\npm\node_modules\pacote\lib\registry.js:98:25)
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error     at async RegistryFetcher.manifest (D:\nodejs\node_modules\npm\node_modules\pacote\lib\registry.js:128:23)
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error     at async getManifest (D:\nodejs\node_modules\npm\node_modules\libnpmexec\lib\index.js:27:22)
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error     at async missingFromTree (D:\nodejs\node_modules\npm\node_modules\libnpmexec\lib\index.js:60:22)
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error     at async D:\nodejs\node_modules\npm\node_modules\libnpmexec\lib\index.js:182:32
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error     at async Promise.all (index 0)
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error     at async exec (D:\nodejs\node_modules\npm\node_modules\libnpmexec\lib\index.js:180:3)
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error     at async Npm.exec (D:\nodejs\node_modules\npm\lib\npm.js:207:9) {
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error   code: 'EPERM',
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error   errno: 'EPERM',
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error   syscall: 'open',
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error   path: 'D:\\nodejs\\node_cache\\_cacache\\tmp\\8e50c2e2',
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error   type: 'system'
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error }
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error The operation was rejected by your operating system.
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error It's possible that the file was already in use (by a text editor or antivirus),
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error or that you lack permissions to access it.
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error If you believe this might be a permissions issue, please double-check the
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error permissions of the file and its containing directories, or try running
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error the command again as root/Administrator.
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm notice
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm notice New major version of npm available! 10.9.2 -> 11.5.2
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm notice Changelog: https://github.com/npm/cli/releases/tag/v11.5.2
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm notice To update run: npm install -g npm@11.5.2
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm notice
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error Log files were not written due to an error writing to the directory: D:\nodejs\node_cache\_logs
2025-08-24 14:53:44 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error You can rerun the command with `--loglevel=verbose` to see the logs in your terminal
2025-08-24 14:58:42 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'chatController' defined in file [D:\daima\new\ruoyi-ai\ruoyi-modules\ruoyi-chat\target\classes\org\ruoyi\chat\controller\chat\ChatController.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sseServiceImpl' defined in file [D:\daima\new\ruoyi-ai\ruoyi-modules\ruoyi-chat\target\classes\org\ruoyi\chat\service\chat\impl\SseServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 4: Error creating bean with name 'chatServiceFactory' defined in file [D:\daima\new\ruoyi-ai\ruoyi-modules\ruoyi-chat\target\classes\org\ruoyi\chat\factory\ChatServiceFactory.class]: Error creating bean with name 'openAIServiceImpl' defined in file [D:\daima\new\ruoyi-ai\ruoyi-modules\ruoyi-chat\target\classes\org\ruoyi\chat\service\chat\impl\OpenAIServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 1: Error creating bean with name 'mcpSyncClients' defined in class path resource [org/springframework/ai/mcp/client/autoconfigure/McpClientAutoConfiguration.class]: Failed to instantiate [java.util.List]: Factory method 'mcpSyncClients' threw exception with message: java.util.concurrent.TimeoutException: Did not observe any item or terminal signal within 300000ms in 'source(MonoCreate)' (and no fallback has been configured)
2025-08-24 14:58:42 [main] INFO  o.r.c.core.manager.ShutdownManager - ====关闭后台任务任务线程池====
2025-08-24 14:58:42 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-08-24 14:58:42 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-08-24 14:58:42 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-08-24 14:58:42 [main] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-08-24 14:58:42 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-08-24 14:58:42 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'chatController' defined in file [D:\daima\new\ruoyi-ai\ruoyi-modules\ruoyi-chat\target\classes\org\ruoyi\chat\controller\chat\ChatController.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sseServiceImpl' defined in file [D:\daima\new\ruoyi-ai\ruoyi-modules\ruoyi-chat\target\classes\org\ruoyi\chat\service\chat\impl\SseServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 4: Error creating bean with name 'chatServiceFactory' defined in file [D:\daima\new\ruoyi-ai\ruoyi-modules\ruoyi-chat\target\classes\org\ruoyi\chat\factory\ChatServiceFactory.class]: Error creating bean with name 'openAIServiceImpl' defined in file [D:\daima\new\ruoyi-ai\ruoyi-modules\ruoyi-chat\target\classes\org\ruoyi\chat\service\chat\impl\OpenAIServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 1: Error creating bean with name 'mcpSyncClients' defined in class path resource [org/springframework/ai/mcp/client/autoconfigure/McpClientAutoConfiguration.class]: Failed to instantiate [java.util.List]: Factory method 'mcpSyncClients' threw exception with message: java.util.concurrent.TimeoutException: Did not observe any item or terminal signal within 300000ms in 'source(MonoCreate)' (and no fallback has been configured)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1381)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:347)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1155)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1121)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1056)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.ruoyi.RuoYiAIApplication.main(RuoYiAIApplication.java:36)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sseServiceImpl' defined in file [D:\daima\new\ruoyi-ai\ruoyi-modules\ruoyi-chat\target\classes\org\ruoyi\chat\service\chat\impl\SseServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 4: Error creating bean with name 'chatServiceFactory' defined in file [D:\daima\new\ruoyi-ai\ruoyi-modules\ruoyi-chat\target\classes\org\ruoyi\chat\factory\ChatServiceFactory.class]: Error creating bean with name 'openAIServiceImpl' defined in file [D:\daima\new\ruoyi-ai\ruoyi-modules\ruoyi-chat\target\classes\org\ruoyi\chat\service\chat\impl\OpenAIServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 1: Error creating bean with name 'mcpSyncClients' defined in class path resource [org/springframework/ai/mcp/client/autoconfigure/McpClientAutoConfiguration.class]: Failed to instantiate [java.util.List]: Factory method 'mcpSyncClients' threw exception with message: java.util.concurrent.TimeoutException: Did not observe any item or terminal signal within 300000ms in 'source(MonoCreate)' (and no fallback has been configured)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1381)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:347)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1667)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1555)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 19 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'chatServiceFactory' defined in file [D:\daima\new\ruoyi-ai\ruoyi-modules\ruoyi-chat\target\classes\org\ruoyi\chat\factory\ChatServiceFactory.class]: Error creating bean with name 'openAIServiceImpl' defined in file [D:\daima\new\ruoyi-ai\ruoyi-modules\ruoyi-chat\target\classes\org\ruoyi\chat\service\chat\impl\OpenAIServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 1: Error creating bean with name 'mcpSyncClients' defined in class path resource [org/springframework/ai/mcp/client/autoconfigure/McpClientAutoConfiguration.class]: Failed to instantiate [java.util.List]: Factory method 'mcpSyncClients' threw exception with message: java.util.concurrent.TimeoutException: Did not observe any item or terminal signal within 300000ms in 'source(MonoCreate)' (and no fallback has been configured)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:608)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:347)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1609)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1555)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 33 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'openAIServiceImpl' defined in file [D:\daima\new\ruoyi-ai\ruoyi-modules\ruoyi-chat\target\classes\org\ruoyi\chat\service\chat\impl\OpenAIServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 1: Error creating bean with name 'mcpSyncClients' defined in class path resource [org/springframework/ai/mcp/client/autoconfigure/McpClientAutoConfiguration.class]: Failed to instantiate [java.util.List]: Factory method 'mcpSyncClients' threw exception with message: java.util.concurrent.TimeoutException: Did not observe any item or terminal signal within 300000ms in 'source(MonoCreate)' (and no fallback has been configured)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1381)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:347)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBeansOfType(DefaultListableBeanFactory.java:721)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBeansOfType(DefaultListableBeanFactory.java:709)
	at org.springframework.context.support.AbstractApplicationContext.getBeansOfType(AbstractApplicationContext.java:1418)
	at org.ruoyi.chat.factory.ChatServiceFactory.setApplicationContext(ChatServiceFactory.java:25)
	at org.springframework.context.support.ApplicationContextAwareProcessor.invokeAwareInterfaces(ApplicationContextAwareProcessor.java:110)
	at org.springframework.context.support.ApplicationContextAwareProcessor.postProcessBeforeInitialization(ApplicationContextAwareProcessor.java:85)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:423)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1804)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:601)
	... 42 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'mcpSyncClients' defined in class path resource [org/springframework/ai/mcp/client/autoconfigure/McpClientAutoConfiguration.class]: Failed to instantiate [java.util.List]: Factory method 'mcpSyncClients' threw exception with message: java.util.concurrent.TimeoutException: Did not observe any item or terminal signal within 300000ms in 'source(MonoCreate)' (and no fallback has been configured)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:657)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:645)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1361)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1191)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:347)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1609)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1555)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 60 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [java.util.List]: Factory method 'mcpSyncClients' threw exception with message: java.util.concurrent.TimeoutException: Did not observe any item or terminal signal within 300000ms in 'source(MonoCreate)' (and no fallback has been configured)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.lambda$instantiate$0(SimpleInstantiationStrategy.java:199)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiateWithFactoryMethod(SimpleInstantiationStrategy.java:88)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:168)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653)
	... 73 common frames omitted
Caused by: reactor.core.Exceptions$ReactiveException: java.util.concurrent.TimeoutException: Did not observe any item or terminal signal within 300000ms in 'source(MonoCreate)' (and no fallback has been configured)
	at reactor.core.Exceptions.propagate(Exceptions.java:410)
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:102)
	at reactor.core.publisher.Mono.block(Mono.java:1779)
	at io.modelcontextprotocol.client.McpSyncClient.initialize(McpSyncClient.java:153)
	at org.springframework.ai.mcp.client.autoconfigure.McpClientAutoConfiguration.mcpSyncClients(McpClientAutoConfiguration.java:168)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.lambda$instantiate$0(SimpleInstantiationStrategy.java:171)
	... 76 common frames omitted
	Suppressed: java.lang.Exception: #block terminated with an error
		at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:104)
		... 84 common frames omitted
Caused by: java.util.concurrent.TimeoutException: Did not observe any item or terminal signal within 300000ms in 'source(MonoCreate)' (and no fallback has been configured)
	at reactor.core.publisher.FluxTimeout$TimeoutMainSubscriber.handleTimeout(FluxTimeout.java:296)
	at reactor.core.publisher.FluxTimeout$TimeoutMainSubscriber.doTimeout(FluxTimeout.java:281)
	at reactor.core.publisher.FluxTimeout$TimeoutTimeoutSubscriber.onNext(FluxTimeout.java:420)
	at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onNext(FluxOnErrorReturn.java:162)
	at reactor.core.publisher.MonoDelay$MonoDelayRunnable.propagateDelay(MonoDelay.java:270)
	at reactor.core.publisher.MonoDelay$MonoDelayRunnable.run(MonoDelay.java:285)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:68)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:28)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-08-24 15:22:18 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-24 15:22:18 [main] INFO  org.ruoyi.RuoYiAIApplication - Starting RuoYiAIApplication using Java 17.0.12 with PID 19268 (D:\daima\new\ruoyi-ai\ruoyi-admin\target\classes started by 86153 in D:\daima\new\ruoyi-ai)
2025-08-24 15:22:18 [main] INFO  org.ruoyi.RuoYiAIApplication - The following 1 profile is active: "dev"
2025-08-24 15:22:32 [main] WARN  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lock4j-com.baomidou.lock.spring.boot.autoconfigure.Lock4jProperties' of type [com.baomidou.lock.spring.boot.autoconfigure.Lock4jProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [meterRegistryPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-24 15:22:32 [main] WARN  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.lock.spring.boot.autoconfigure.LockAutoConfiguration' of type [com.baomidou.lock.spring.boot.autoconfigure.LockAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [meterRegistryPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-24 15:22:32 [main] WARN  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lockKeyBuilder' of type [com.baomidou.lock.DefaultLockKeyBuilder] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [meterRegistryPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-24 15:22:32 [main] WARN  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lockFailureStrategy' of type [com.baomidou.lock.DefaultLockFailureStrategy] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [meterRegistryPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-24 15:22:32 [main] WARN  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lockInterceptor' of type [com.baomidou.lock.aop.LockInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [meterRegistryPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-24 15:22:32 [main] WARN  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lockAnnotationAdvisor' of type [com.baomidou.lock.aop.LockAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [meterRegistryPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-24 15:22:32 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-24 15:22:33 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-08-24 15:22:33 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-08-24 15:22:34 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@a94c8e3
2025-08-24 15:22:34 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-08-24 15:22:34 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-08-24 15:22:34 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-24 15:22:36 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "org.ruoyi.system.domain.CommentGoodbad".
2025-08-24 15:22:36 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class org.ruoyi.system.domain.CommentGoodbad ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-24 15:22:36 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "org.ruoyi.system.domain.Comment".
2025-08-24 15:22:36 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class org.ruoyi.system.domain.Comment ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-24 15:22:36 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "org.ruoyi.system.domain.NewsComment".
2025-08-24 15:22:36 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class org.ruoyi.system.domain.NewsComment ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-24 15:22:37 [main] INFO  o.r.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-08-24 15:22:37 [main] INFO  o.r.common.redis.config.RedisConfig - 初始化 redis 配置
2025-08-24 15:22:37 [main] INFO  org.redisson.Version - Redisson 3.20.1
2025-08-24 15:22:37 [redisson-netty-2-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-08-24 15:22:38 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-08-24 15:22:39 [main] INFO  o.r.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-08-24 15:22:40 [HttpClient-1-Worker-1] INFO  i.m.client.McpAsyncClient - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[experimental=null, logging=LoggingCapabilities[], prompts=null, resources=null, tools=ToolCapabilities[listChanged=true]], Info: Implementation[name=ruoyi-mcp-serve, version=1.0.0] and Instructions null
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error code EPERM
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error syscall open
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error path D:\nodejs\node_cache\_cacache\tmp\8559c774
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error errno EPERM
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error FetchError: Invalid response body while trying to fetch https://registry.npmmirror.com/@modelcontextprotocol%2fserver-filesystem: EPERM: operation not permitted, open 'D:\nodejs\node_cache\_cacache\tmp\8559c774'
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error     at D:\nodejs\node_modules\npm\node_modules\minipass-fetch\lib\body.js:170:15
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error     at async Response.json (D:\nodejs\node_modules\npm\node_modules\minipass-fetch\lib\body.js:75:17)
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error     at async RegistryFetcher.packument (D:\nodejs\node_modules\npm\node_modules\pacote\lib\registry.js:98:25)
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error     at async RegistryFetcher.manifest (D:\nodejs\node_modules\npm\node_modules\pacote\lib\registry.js:128:23)
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error     at async getManifest (D:\nodejs\node_modules\npm\node_modules\libnpmexec\lib\index.js:27:22)
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error     at async missingFromTree (D:\nodejs\node_modules\npm\node_modules\libnpmexec\lib\index.js:60:22)
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error     at async D:\nodejs\node_modules\npm\node_modules\libnpmexec\lib\index.js:182:32
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error     at async Promise.all (index 0)
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error     at async exec (D:\nodejs\node_modules\npm\node_modules\libnpmexec\lib\index.js:180:3)
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error     at async Npm.exec (D:\nodejs\node_modules\npm\lib\npm.js:207:9) {
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error   code: 'EPERM',
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error   errno: 'EPERM',
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error   syscall: 'open',
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error   path: 'D:\\nodejs\\node_cache\\_cacache\\tmp\\8559c774',
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error   type: 'system'
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error }
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error The operation was rejected by your operating system.
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error It's possible that the file was already in use (by a text editor or antivirus),
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error or that you lack permissions to access it.
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error If you believe this might be a permissions issue, please double-check the
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error permissions of the file and its containing directories, or try running
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error the command again as root/Administrator.
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm notice
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm notice New major version of npm available! 10.9.2 -> 11.5.2
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm notice Changelog: https://github.com/npm/cli/releases/tag/v11.5.2
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm notice To update run: npm install -g npm@11.5.2
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm notice
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error Log files were not written due to an error writing to the directory: D:\nodejs\node_cache\_logs
2025-08-24 15:23:00 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: npm error You can rerun the command with `--loglevel=verbose` to see the logs in your terminal
2025-08-24 15:25:13 [HttpClient-1-Worker-4] ERROR i.m.c.t.HttpClientSseClientTransport - SSE connection error
java.io.IOException: chunked transfer encoding, state: READING_LENGTH
	at java.net.http/jdk.internal.net.http.common.Utils.wrapWithExtraDetail(Utils.java:351)
	at java.net.http/jdk.internal.net.http.Http1Response$BodyReader.onReadError(Http1Response.java:760)
	at java.net.http/jdk.internal.net.http.Http1AsyncReceiver.checkForErrors(Http1AsyncReceiver.java:302)
	at java.net.http/jdk.internal.net.http.Http1AsyncReceiver.flush(Http1AsyncReceiver.java:268)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$LockingRestartableTask.run(SequentialScheduler.java:205)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$CompleteRestartableTask.run(SequentialScheduler.java:149)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$SchedulableTask.run(SequentialScheduler.java:230)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.io.EOFException: EOF reached while reading
	at java.net.http/jdk.internal.net.http.Http1AsyncReceiver$Http1TubeSubscriber.onComplete(Http1AsyncReceiver.java:596)
	at java.net.http/jdk.internal.net.http.SocketTube$InternalReadPublisher$ReadSubscription.signalCompletion(SocketTube.java:640)
	at java.net.http/jdk.internal.net.http.SocketTube$InternalReadPublisher$InternalReadSubscription.read(SocketTube.java:845)
	at java.net.http/jdk.internal.net.http.SocketTube$SocketFlowTask.run(SocketTube.java:181)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$SchedulableTask.run(SequentialScheduler.java:230)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler.runOrSchedule(SequentialScheduler.java:303)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler.runOrSchedule(SequentialScheduler.java:256)
	at java.net.http/jdk.internal.net.http.SocketTube$InternalReadPublisher$InternalReadSubscription.signalReadable(SocketTube.java:774)
	at java.net.http/jdk.internal.net.http.SocketTube$InternalReadPublisher$ReadEvent.signalEvent(SocketTube.java:957)
	at java.net.http/jdk.internal.net.http.SocketTube$SocketFlowEvent.handle(SocketTube.java:253)
	at java.net.http/jdk.internal.net.http.HttpClientImpl$SelectorManager.handleEvent(HttpClientImpl.java:982)
	at java.net.http/jdk.internal.net.http.HttpClientImpl$SelectorManager.lambda$run$3(HttpClientImpl.java:937)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.net.http/jdk.internal.net.http.HttpClientImpl$SelectorManager.run(HttpClientImpl.java:937)
2025-08-24 15:25:13 [ForkJoinPool.commonPool-worker-2] ERROR i.m.c.t.HttpClientSseClientTransport - SSE connection error
java.util.concurrent.CompletionException: java.io.IOException: chunked transfer encoding, state: READING_LENGTH
	at java.base/java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:332)
	at java.base/java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:347)
	at java.base/java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:874)
	at java.base/java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:841)
	at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
	at java.base/java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:2162)
	at java.net.http/jdk.internal.net.http.ResponseSubscribers.lambda$getBodyAsync$2(ResponseSubscribers.java:1155)
	at java.base/java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:863)
	at java.base/java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:841)
	at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
	at java.base/java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:2162)
	at java.net.http/jdk.internal.net.http.LineSubscriberAdapter$LineSubscription.loop(LineSubscriberAdapter.java:410)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$LockingRestartableTask.run(SequentialScheduler.java:205)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$CompleteRestartableTask.run(SequentialScheduler.java:149)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$SchedulableTask.run(SequentialScheduler.java:230)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler.runOrSchedule(SequentialScheduler.java:303)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler.runOrSchedule(SequentialScheduler.java:256)
	at java.net.http/jdk.internal.net.http.LineSubscriberAdapter$LineSubscription.signalError(LineSubscriberAdapter.java:199)
	at java.net.http/jdk.internal.net.http.LineSubscriberAdapter.onError(LineSubscriberAdapter.java:105)
	at java.net.http/jdk.internal.net.http.Http1Response$Http1BodySubscriber.propagateError(Http1Response.java:327)
	at java.net.http/jdk.internal.net.http.Http1Response$Http1BodySubscriber.complete(Http1Response.java:356)
	at java.net.http/jdk.internal.net.http.Http1Response$Http1BodySubscriber.onError(Http1Response.java:386)
	at java.net.http/jdk.internal.net.http.Http1Response.lambda$readBody$2(Http1Response.java:468)
	at java.base/java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:863)
	at java.base/java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:841)
	at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
	at java.base/java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:2162)
	at java.net.http/jdk.internal.net.http.Http1Response.onReadError(Http1Response.java:554)
	at java.net.http/jdk.internal.net.http.Http1Response$BodyReader.onReadError(Http1Response.java:761)
	at java.net.http/jdk.internal.net.http.Http1AsyncReceiver.checkForErrors(Http1AsyncReceiver.java:302)
	at java.net.http/jdk.internal.net.http.Http1AsyncReceiver.flush(Http1AsyncReceiver.java:268)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$LockingRestartableTask.run(SequentialScheduler.java:205)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$CompleteRestartableTask.run(SequentialScheduler.java:149)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$SchedulableTask.run(SequentialScheduler.java:230)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.io.IOException: chunked transfer encoding, state: READING_LENGTH
	at java.net.http/jdk.internal.net.http.common.Utils.wrapWithExtraDetail(Utils.java:351)
	at java.net.http/jdk.internal.net.http.Http1Response$BodyReader.onReadError(Http1Response.java:760)
	... 8 common frames omitted
Caused by: java.io.EOFException: EOF reached while reading
	at java.net.http/jdk.internal.net.http.Http1AsyncReceiver$Http1TubeSubscriber.onComplete(Http1AsyncReceiver.java:596)
	at java.net.http/jdk.internal.net.http.SocketTube$InternalReadPublisher$ReadSubscription.signalCompletion(SocketTube.java:640)
	at java.net.http/jdk.internal.net.http.SocketTube$InternalReadPublisher$InternalReadSubscription.read(SocketTube.java:845)
	at java.net.http/jdk.internal.net.http.SocketTube$SocketFlowTask.run(SocketTube.java:181)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$SchedulableTask.run(SequentialScheduler.java:230)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler.runOrSchedule(SequentialScheduler.java:303)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler.runOrSchedule(SequentialScheduler.java:256)
	at java.net.http/jdk.internal.net.http.SocketTube$InternalReadPublisher$InternalReadSubscription.signalReadable(SocketTube.java:774)
	at java.net.http/jdk.internal.net.http.SocketTube$InternalReadPublisher$ReadEvent.signalEvent(SocketTube.java:957)
	at java.net.http/jdk.internal.net.http.SocketTube$SocketFlowEvent.handle(SocketTube.java:253)
	at java.net.http/jdk.internal.net.http.HttpClientImpl$SelectorManager.handleEvent(HttpClientImpl.java:982)
	at java.net.http/jdk.internal.net.http.HttpClientImpl$SelectorManager.lambda$run$3(HttpClientImpl.java:937)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.net.http/jdk.internal.net.http.HttpClientImpl$SelectorManager.run(HttpClientImpl.java:937)
2025-08-24 15:25:13 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'chatController' defined in file [D:\daima\new\ruoyi-ai\ruoyi-modules\ruoyi-chat\target\classes\org\ruoyi\chat\controller\chat\ChatController.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sseServiceImpl' defined in file [D:\daima\new\ruoyi-ai\ruoyi-modules\ruoyi-chat\target\classes\org\ruoyi\chat\service\chat\impl\SseServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 4: Error creating bean with name 'chatServiceFactory' defined in file [D:\daima\new\ruoyi-ai\ruoyi-modules\ruoyi-chat\target\classes\org\ruoyi\chat\factory\ChatServiceFactory.class]: Error creating bean with name 'openAIServiceImpl' defined in file [D:\daima\new\ruoyi-ai\ruoyi-modules\ruoyi-chat\target\classes\org\ruoyi\chat\service\chat\impl\OpenAIServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 1: Error creating bean with name 'mcpSyncClients' defined in class path resource [org/springframework/ai/mcp/client/autoconfigure/McpClientAutoConfiguration.class]: Failed to instantiate [java.util.List]: Factory method 'mcpSyncClients' threw exception with message: java.lang.InterruptedException
2025-08-24 15:25:13 [main] INFO  o.r.c.core.manager.ShutdownManager - ====关闭后台任务任务线程池====
2025-08-24 15:25:13 [redisson-netty-2-1] ERROR i.n.u.c.D.rejectedExecution - Failed to submit a listener notification task. Event loop shut down?
java.util.concurrent.RejectedExecutionException: event executor terminated
	at io.netty.util.concurrent.SingleThreadEventExecutor.reject(SingleThreadEventExecutor.java:934)
	at io.netty.util.concurrent.SingleThreadEventExecutor.offerTask(SingleThreadEventExecutor.java:353)
	at io.netty.util.concurrent.SingleThreadEventExecutor.addTask(SingleThreadEventExecutor.java:346)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:836)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute0(SingleThreadEventExecutor.java:827)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:817)
	at io.netty.util.concurrent.DefaultPromise.safeExecute(DefaultPromise.java:862)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:500)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setSuccess0(DefaultPromise.java:625)
	at io.netty.util.concurrent.DefaultPromise.setSuccess(DefaultPromise.java:97)
	at io.netty.channel.group.DefaultChannelGroupFuture.setSuccess0(DefaultChannelGroupFuture.java:200)
	at io.netty.channel.group.DefaultChannelGroupFuture.access$400(DefaultChannelGroupFuture.java:41)
	at io.netty.channel.group.DefaultChannelGroupFuture$1.operationComplete(DefaultChannelGroupFuture.java:75)
	at io.netty.channel.group.DefaultChannelGroupFuture$1.operationComplete(DefaultChannelGroupFuture.java:48)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:583)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:559)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setSuccess0(DefaultPromise.java:625)
	at io.netty.util.concurrent.DefaultPromise.trySuccess(DefaultPromise.java:105)
	at io.netty.channel.DefaultChannelPromise.trySuccess(DefaultChannelPromise.java:84)
	at io.netty.channel.AbstractChannel$CloseFuture.setClosed(AbstractChannel.java:1161)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.doClose0(AbstractChannel.java:753)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.close(AbstractChannel.java:729)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.close(AbstractChannel.java:619)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.close(DefaultChannelPipeline.java:1299)
	at io.netty.channel.AbstractChannelHandlerContext.invokeClose(AbstractChannelHandlerContext.java:755)
	at io.netty.channel.AbstractChannelHandlerContext.close(AbstractChannelHandlerContext.java:733)
	at io.netty.channel.AbstractChannelHandlerContext.close(AbstractChannelHandlerContext.java:560)
	at io.netty.channel.DefaultChannelPipeline.close(DefaultChannelPipeline.java:906)
	at io.netty.channel.AbstractChannel.close(AbstractChannel.java:243)
	at org.redisson.client.RedisConnection.lambda$close$3(RedisConnection.java:304)
	at java.base/java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:863)
	at java.base/java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:841)
	at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
	at java.base/java.util.concurrent.CompletableFuture.complete(CompletableFuture.java:2147)
	at org.redisson.client.handler.CommandDecoder.completeResponse(CommandDecoder.java:470)
	at org.redisson.client.handler.CommandDecoder.handleResult(CommandDecoder.java:464)
	at org.redisson.client.handler.CommandDecoder.decode(CommandDecoder.java:340)
	at org.redisson.client.handler.CommandDecoder.decodeCommand(CommandDecoder.java:205)
	at org.redisson.client.handler.CommandDecoder.decode(CommandDecoder.java:144)
	at org.redisson.client.handler.CommandDecoder.decode(CommandDecoder.java:120)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:530)
	at io.netty.handler.codec.ReplayingDecoder.callDecode(ReplayingDecoder.java:366)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-08-24 15:25:13 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-08-24 15:25:13 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-08-24 15:25:13 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-08-24 15:25:13 [main] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-08-24 15:25:13 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-08-24 15:29:58 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-24 15:29:58 [main] INFO  org.ruoyi.RuoYiAIApplication - Starting RuoYiAIApplication using Java 17.0.12 with PID 2284 (D:\daima\new\ruoyi-ai\ruoyi-admin\target\classes started by 86153 in D:\daima\new\ruoyi-ai)
2025-08-24 15:29:58 [main] INFO  org.ruoyi.RuoYiAIApplication - The following 1 profile is active: "dev"
2025-08-24 15:30:03 [main] WARN  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lock4j-com.baomidou.lock.spring.boot.autoconfigure.Lock4jProperties' of type [com.baomidou.lock.spring.boot.autoconfigure.Lock4jProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [meterRegistryPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-24 15:30:03 [main] WARN  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.lock.spring.boot.autoconfigure.LockAutoConfiguration' of type [com.baomidou.lock.spring.boot.autoconfigure.LockAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [meterRegistryPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-24 15:30:03 [main] WARN  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lockKeyBuilder' of type [com.baomidou.lock.DefaultLockKeyBuilder] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [meterRegistryPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-24 15:30:03 [main] WARN  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lockFailureStrategy' of type [com.baomidou.lock.DefaultLockFailureStrategy] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [meterRegistryPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-24 15:30:03 [main] WARN  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lockInterceptor' of type [com.baomidou.lock.aop.LockInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [meterRegistryPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-24 15:30:03 [main] WARN  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lockAnnotationAdvisor' of type [com.baomidou.lock.aop.LockAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [meterRegistryPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-24 15:30:04 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-24 15:30:04 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-08-24 15:30:04 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-08-24 15:30:05 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@8a0a1d1
2025-08-24 15:30:05 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-08-24 15:30:05 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-08-24 15:30:05 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-24 15:30:06 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "org.ruoyi.system.domain.CommentGoodbad".
2025-08-24 15:30:06 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class org.ruoyi.system.domain.CommentGoodbad ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-24 15:30:06 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "org.ruoyi.system.domain.Comment".
2025-08-24 15:30:06 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class org.ruoyi.system.domain.Comment ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-24 15:30:06 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "org.ruoyi.system.domain.NewsComment".
2025-08-24 15:30:06 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class org.ruoyi.system.domain.NewsComment ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-24 15:30:07 [main] INFO  o.r.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-08-24 15:30:07 [main] INFO  o.r.common.redis.config.RedisConfig - 初始化 redis 配置
2025-08-24 15:30:07 [main] INFO  org.redisson.Version - Redisson 3.20.1
2025-08-24 15:30:08 [redisson-netty-2-4] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-08-24 15:30:08 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-08-24 15:30:09 [main] INFO  o.r.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-08-24 15:30:10 [HttpClient-1-Worker-1] INFO  i.m.client.McpAsyncClient - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[experimental=null, logging=LoggingCapabilities[], prompts=null, resources=null, tools=ToolCapabilities[listChanged=true]], Info: Implementation[name=ruoyi-mcp-serve, version=1.0.0] and Instructions null
2025-08-24 15:30:13 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: Secure MCP Filesystem Server running on stdio
2025-08-24 15:30:13 [pool-3-thread-1] INFO  i.m.client.McpAsyncClient - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[experimental=null, logging=null, prompts=null, resources=null, tools=ToolCapabilities[listChanged=null]], Info: Implementation[name=secure-filesystem-server, version=0.2.0] and Instructions null
2025-08-24 15:30:13 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: Client does not support MCP Roots, using allowed directories set from server args: [ 'D:\\ruoyi-mcp' ]
2025-08-24 15:30:15 [pool-9-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: [2025-08-24T07:30:15.672Z] Starting Search1API MCP server
2025-08-24 15:30:15 [pool-9-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: [2025-08-24T07:30:15.673Z] Creating Search1API MCP server
2025-08-24 15:30:15 [pool-9-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: [2025-08-24T07:30:15.674Z] Server started successfully
2025-08-24 15:30:15 [pool-9-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: [2025-08-24T07:30:15.674Z] Server started successfully
2025-08-24 15:30:15 [pool-7-thread-1] INFO  i.m.client.McpAsyncClient - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[experimental=null, logging=null, prompts=null, resources=ResourceCapabilities[subscribe=null, listChanged=null], tools=ToolCapabilities[listChanged=null]], Info: Implementation[name=search1api-server, version=1.0.0] and Instructions null
2025-08-24 15:30:20 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-08-24 15:30:20 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-24 15:30:20 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-24 15:30:20 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-24 15:30:20 [main] INFO  org.ruoyi.RuoYiAIApplication - Started RuoYiAIApplication in 22.759 seconds (process running for 23.92)
2025-08-24 15:30:20 [main] INFO  o.r.c.c.l.WebSocketTopicListener - 初始化WebSocket主题订阅监听器成功
2025-08-24 15:30:20 [main] INFO  o.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-08-24 15:30:20 [RMI TCP Connection(4)-172.30.16.1] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-24 15:30:21 [RMI TCP Connection(3)-172.30.16.1] WARN  o.s.b.a.e.ElasticsearchRestClientHealthIndicator - Elasticsearch health check failed
java.net.ConnectException: Connection refused: no further information
	at org.elasticsearch.client.RestClient.extractAndWrapCause(RestClient.java:934)
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:304)
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:292)
	at org.springframework.boot.actuate.elasticsearch.ElasticsearchRestClientHealthIndicator.doHealthCheck(ElasticsearchRestClientHealthIndicator.java:60)
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82)
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:82)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:76)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:66)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:281)
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74)
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:124)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:814)
	at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:802)
	at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1472)
	at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1310)
	at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1405)
	at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829)
	at java.base/jdk.internal.reflect.GeneratedMethodAccessor76.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.rmi/sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:360)
	at java.rmi/sun.rmi.transport.Transport$1.run(Transport.java:200)
	at java.rmi/sun.rmi.transport.Transport$1.run(Transport.java:197)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:712)
	at java.rmi/sun.rmi.transport.Transport.serviceCall(Transport.java:196)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:587)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:828)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:705)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:704)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:946)
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvent(DefaultConnectingIOReactor.java:174)
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvents(DefaultConnectingIOReactor.java:148)
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor.execute(AbstractMultiworkerIOReactor.java:351)
	at org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager.execute(PoolingNHttpClientConnectionManager.java:221)
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase$1.run(CloseableHttpAsyncClientBase.java:64)
	... 1 common frames omitted
2025-08-24 15:34:04 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 15:34:08 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 15:34:08 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 15:34:08 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 15:34:10 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 15:34:10 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 15:34:10 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 15:34:24 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 15:34:24 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 15:34:24 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 15:34:26 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 15:35:26 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 15:35:26 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 15:35:53 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 15:36:10 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:--> POST https://api.ppinfra.com/openai/v1/chat/completions
2025-08-24 15:36:10 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Content-Type: application/json; charset=utf-8
2025-08-24 15:36:10 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Content-Length: 855
2025-08-24 15:36:10 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Accept: text/event-stream
2025-08-24 15:36:10 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:--> END POST
2025-08-24 15:36:11 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:<-- 200 https://api.ppinfra.com/openai/v1/chat/completions (1057ms)
2025-08-24 15:36:11 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:date: Sun, 24 Aug 2025 07:34:56 GMT
2025-08-24 15:36:11 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:content-type: text/event-stream
2025-08-24 15:36:11 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:cache-control: no-cache
2025-08-24 15:36:11 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:x-request-id: 
2025-08-24 15:36:11 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:x-trace-id: f0db9011a8edecae95290a62c3b48fba
2025-08-24 15:36:11 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:<-- END HTTP
2025-08-24 15:36:11 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.l.SSEEventSourceListener - OpenAI建立sse连接...
2025-08-24 15:36:12 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 15:36:12 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.l.SSEEventSourceListener - OpenAI关闭sse连接...
2025-08-24 15:36:57 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 15:37:46 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:--> POST https://api.ppinfra.com/openai/v1/chat/completions
2025-08-24 15:37:46 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Content-Type: application/json; charset=utf-8
2025-08-24 15:37:46 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Content-Length: 22808
2025-08-24 15:37:46 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Accept: text/event-stream
2025-08-24 15:37:46 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:--> END POST
2025-08-24 15:37:49 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:<-- 200 https://api.ppinfra.com/openai/v1/chat/completions (2263ms)
2025-08-24 15:37:49 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:date: Sun, 24 Aug 2025 07:36:34 GMT
2025-08-24 15:37:49 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:content-type: text/event-stream
2025-08-24 15:37:49 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:cache-control: no-cache
2025-08-24 15:37:49 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:x-request-id: 
2025-08-24 15:37:49 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:x-trace-id: 471296bf77970c58f270b6f70b8e65fb
2025-08-24 15:37:49 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:<-- END HTTP
2025-08-24 15:37:49 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.l.SSEEventSourceListener - OpenAI建立sse连接...
2025-08-24 15:38:38 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 15:38:38 [OkHttp https://api.ppinfra.com/...] INFO  o.r.c.l.SSEEventSourceListener - OpenAI关闭sse连接...
2025-08-24 15:46:31 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 15:50:02 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 15:50:02 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 15:50:37 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 15:50:37 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:11:28 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:11:28 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:11:33 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:11:33 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:11:36 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:11:36 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:12:23 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:12:23 [XNIO-1 task-2] ERROR o.r.c.s.chat.impl.SseServiceImpl - 不支持的模型类别: FASTGPT-fastgpt
java.lang.IllegalArgumentException: 不支持的模型类别: FASTGPT-fastgpt
	at org.ruoyi.chat.factory.ChatServiceFactory.getChatService(ChatServiceFactory.java:39)
	at org.ruoyi.chat.service.chat.impl.SseServiceImpl.sseChat(SseServiceImpl.java:117)
	at org.ruoyi.chat.controller.chat.ChatController.sseChat(ChatController.java:41)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:547)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.ruoyi.common.web.filter.RepeatableFilter.doFilter(RepeatableFilter.java:32)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-08-24 16:12:23 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:14:31 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:14:31 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:14:36 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:14:38 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:14:43 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:14:53 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:14:55 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:15:01 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:22:16 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:22:18 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:22:18 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:22:19 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:22:19 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:22:21 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:22:21 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:22:24 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:22:25 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:22:26 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:22:26 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:22:27 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:22:28 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:22:28 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:22:29 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:22:30 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:22:30 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:22:33 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:22:33 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:22:35 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:22:35 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:22:36 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:22:36 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-24 16:22:37 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
